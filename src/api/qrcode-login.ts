import { httpAdaptor } from './api.adaptor';

// 扫码登录相关接口类型定义
export interface QrcodeGenerateResponse {
  token: string;
  qrcodeData: string;
  expiresIn: number;
}

export interface QrcodeStatusResponse {
  status: 'waiting' | 'scanned' | 'confirmed' | 'expired' | 'cancelled';
  userInfo?: {
    username: string;
    employeeId: string;
    token: string;
  };
}

export interface QrcodeLoginRequest {
  token: string;
  userInfo: {
    username: string;
    employeeId: string;
  };
}

/**
 * 生成扫码登录二维码
 * 
 * @returns Promise<QrcodeGenerateResponse>
 */
export function generateQrcodeLogin() {
  return httpAdaptor.post<QrcodeGenerateResponse>({
    url: '/v1/open-api/user-login/qrcode/generate',
    data: {},
  });
}

/**
 * 查询二维码扫码状态
 * 
 * @param token 二维码token
 * @returns Promise<QrcodeStatusResponse>
 */
export function getQrcodeStatus(token: string) {
  return httpAdaptor.get<QrcodeStatusResponse>({
    url: '/v1/open-api/user-login/qrcode/status',
    query: { token },
  });
}

/**
 * 确认扫码登录
 * 
 * @param data 登录确认数据
 * @returns Promise<void>
 */
export function confirmQrcodeLogin(data: QrcodeLoginRequest) {
  return httpAdaptor.post<void>({
    url: '/v1/open-api/user-login/qrcode/confirm',
    data,
  });
}

/**
 * 取消扫码登录
 * 
 * @param token 二维码token
 * @returns Promise<void>
 */
export function cancelQrcodeLogin(token: string) {
  return httpAdaptor.post<void>({
    url: '/v1/open-api/user-login/qrcode/cancel',
    data: { token },
  });
}

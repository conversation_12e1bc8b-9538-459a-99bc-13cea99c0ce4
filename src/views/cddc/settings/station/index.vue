<script lang="ts" setup>
import { BasicForm, type FormSchema, useForm } from '@geega-ui-plus/geega-ui';
import {
  V1ManageSysUnitDeleteIdDelete,
  V1ManageSysUnitId,
  V1ManageSysUnitPost,
  V1ManageSysUnitPut,
  V1ManageSysUnitTerminalCaptureIp,
  V1ManageSysUnitTreePost,
} from '/@/api/cddc.req';
import { useAsyncData, useLoadingFn } from '/@/composables';
import { filterTree, findNodeInTree, traverseTree } from '/@/utils/tree';
import type { LocationListElement, V1ManageSysUnitPutRequestBody } from '/@/api/cddc.model';
import { computed, onMounted, reactive, ref } from 'vue';
import { PlusOutlined, DeleteOutlined } from '@geega-ui-plus/icons-vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import StationInfo from './components/StationInfo.vue';
import { componentDefaultProps } from '/@/logics/defaultFormConfig';
import PicMarkTool from '/@/components/MarkTool/PicMarkTool.vue';

const { createDeteleConfirm } = useMessage();

const treeState = reactive({
  search: '',
  selectedKeys: [] as string[],
  expandedKeys: [] as string[],
});

const markToolState = reactive({
  src: '',
  visible: false,
  cameraNo: -1,
  points: [],
});

const treesData = useAsyncData(async () => {
  const resp = await V1ManageSysUnitTreePost();

  traverseTree(resp, (node) => {
    node.key = node.id;

    node.children = node.childNodes;
  });

  return resp;
}, []);

const currentDetailInfo = useAsyncData(V1ManageSysUnitId, {});

const parentNodeOptions = computed(() => {
  const nodes: any[] = [
    {
      label: '顶级',
      value: '-1',
    },
  ];

  traverseTree(treesData.data.value, (node) => {
    if (node.type === NodeTypeEnum.NODE) {
      nodes.push({
        label: node.name,
        value: node.id?.toString(),
      });
    }
  });

  return nodes;
});

const filteredTreeData = computed(() => {
  const data = treesData.data.value;

  if (!treeState.search) {
    return data;
  }

  return filterTree(data, (node) => !!node.name?.includes(treeState.search));
});

enum NodeTypeEnum {
  NODE = 'NODE',
  STATION = 'STATION',
}

const NodeTypeOptions = [
  {
    label: '节点',
    value: NodeTypeEnum.NODE,
  },
  {
    label: '工位',
    value: NodeTypeEnum.STATION,
  },
];

const isEditing = ref(false);

const basicSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '编号',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: isEditing,
      placeholder: '请输入编号',
      maxlength: 32,
    },
  },
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入名称',
      maxlength: 32,
    },
  },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    required: true,
    componentProps: {
      disabled: isEditing,
      options: NodeTypeOptions,
      placeholder: '请选择类型',
      async onChange(val) {
        const showStationSchemas = val === NodeTypeEnum.STATION;
        await updateFormSchemas(showStationSchemas);
      },
    },
  },
  {
    field: 'parentId',
    label: '父节点',
    component: 'Select',
    required: true,
    componentProps: {
      disabled: isEditing,
      placeholder: '请选择父节点',
      options: parentNodeOptions,
    },
  },
];

const stationSchemas: FormSchema[] = [
  {
    field: 'ipCameraOne',
    label: '摄像机1',
    component: 'Input',
    required: false,
    componentProps: {
      ...componentDefaultProps.input,
      placeholder: '请输入摄像机1的IP',
    },
  },
  {
    field: 'ipCameraTwo',
    label: '摄像机2',
    component: 'Input',
    required: false,
    componentProps: {
      ...componentDefaultProps.input,
      placeholder: '请输入摄像机2的IP',
    },
  },
  {
    field: 'cardDeviceIp',
    label: '刷卡设备',
    component: 'Input',
    required: true,
    componentProps: {
      ...componentDefaultProps.input,
      placeholder: '请输入刷卡设备的IP',
    },
  },
  {
    field: 'ipTerminal',
    label: '显示终端',
    component: 'Input',
    required: true,
    componentProps: {
      ...componentDefaultProps.input,
      placeholder: '请输入显示终端的IP',
    },
  },
  {
    field: 'cameraOneCalibration',
    label: '相机标定1',
    component: 'Input',
    show: false,
  },
  {
    field: 'cameraTwoCalibration',
    label: '相机标定1',
    component: 'Input',
    show: false,
  },
];

const [registerForm, formActions] = useForm({
  baseColProps: {
    span: 8,
  },
  rowProps: {
    gutter: 24,
  },
  actionColOptions: {
    span: 24,
  },
  submitFunc: submitDetailModification,
  resetButtonOptions: {
    // @ts-ignore
    onClick: resetFormDetailData,
  },
  submitButtonOptions: {
    text: '保存',
  },
  schemas: [...basicSchemas, ...stationSchemas],
});

const updateStationDetail = useLoadingFn(_updateStationDetail);

onMounted(() => {
  reloadTreeData();
});

async function updateFormSchemas(showStationSchemas: boolean) {
  const schemas = [
    ...basicSchemas,
    ...stationSchemas.map((item) => ({ ...item, ifShow: showStationSchemas })),
  ];

  await formActions.updateSchema(schemas);
}

async function reloadTreeData() {
  await treesData.load();

  {
    // update expanded keys
    const expandedKeys: string[] = [];
    traverseTree(treesData.data.value, (node) => {
      if (node.childNodes?.length) {
        expandedKeys.push(node.id!);
      }
    });

    treeState.expandedKeys = expandedKeys;
  }

  const selectedKey = treeState.selectedKeys.at(0);
  const oldSelectedNode = findNodeInTree(treesData.data.value, (node) => node.id === selectedKey);

  const selectedNode = oldSelectedNode || treesData.data.value.at(0);

  if (selectedNode) {
    updateStationDetail(selectedNode);
  } else {
    onAddBtnClick({ parentId: '-1' });
  }
}

async function onSelectTreeNode(node: LocationListElement) {
  await updateStationDetail(node);
}

async function _updateStationDetail(node: LocationListElement) {
  isEditing.value = true;
  treeState.selectedKeys = [node.id!];

  await currentDetailInfo.load({
    id: node.id!,
  });

  const resp = currentDetailInfo.data.value;

  const showStationSchemas = node.type === NodeTypeEnum.STATION;
  updateFormSchemas(showStationSchemas);

  formActions.setFieldsValue({
    ...resp.terminalDTO,
    ...resp,
    parentName: node.parentName,
  });
}

async function submitDetailModification() {
  const values = await formActions.validate();

  const params: V1ManageSysUnitPutRequestBody = {
    ...values,
    code: values.code,
    name: values.name,
    parentId: values.parentId,
    type: values.type,
    terminal: {
      ipCameraOne: values.ipCameraOne,
      ipCameraTwo: values.ipCameraTwo,
      ipTerminal: values.ipTerminal,
      cardDeviceIp: values.cardDeviceIp,
      cameraOneCalibration: values.cameraOneCalibration,
      cameraTwoCalibration: values.cameraTwoCalibration,
    },
  };

  if (values.id) {
    await V1ManageSysUnitPost({
      id: values.id,
      ...params,
    });
    message.success('修改成功');
  } else {
    const id = await V1ManageSysUnitPut(params);
    treeState.selectedKeys = [id];
    message.success('创建成功');
  }

  await reloadTreeData();
}

async function resetFormDetailData() {
  const currentData = currentDetailInfo.data.value;

  await formActions.setFieldsValue({
    ...currentData.terminalDTO,
    ...currentData,
  });
}

async function onAddBtnClick(node?: LocationListElement) {
  isEditing.value = false;

  const detailInfo = {
    parentId: node?.id || '-1',
  };

  currentDetailInfo.update(detailInfo);

  await formActions.resetFields();
  await formActions.setFieldsValue(detailInfo);

  updateFormSchemas(false);

  await formActions.clearValidate();
}

function onDeleteBtnClick(node: LocationListElement) {
  createDeteleConfirm({
    content: `请确认是否删除[${node.name}]`,
    async onOk() {
      await V1ManageSysUnitDeleteIdDelete({
        id: node.id!,
      });

      await reloadTreeData();
    },
  });
}

function getCameraFieldName(cameraNo: number, field: 'ip' | 'points'): string {
  const fieldMap = {
    1: {
      ip: 'ipCameraOne',
      points: 'cameraOneCalibration',
    },
    2: {
      ip: 'ipCameraTwo',
      points: 'cameraTwoCalibration',
    },
  };

  return fieldMap[cameraNo][field];
}

function getCameraField(cameraNo: number, field: 'ip' | 'points'): string {
  const formValue = formActions.getFieldsValue();

  const filedName = getCameraFieldName(cameraNo, field);
  return formValue[filedName];
}

function showMarkEditor(cameraNo: number) {
  markToolState.cameraNo = cameraNo;
  const ip = getCameraField(cameraNo, 'ip');

  if (!ip) {
    message.warn('请先设置相机IP');
    return;
  }

  const pointsStr = getCameraField(cameraNo, 'points');

  markToolState.points = parsePoints(pointsStr);
  markToolState.visible = true;

  reloadMarkImage();
}

function parsePoints(pointsStr: string) {
  try {
    return JSON.parse(pointsStr);
  } catch (error) {
    return [];
  }
}

async function reloadMarkImage() {
  const ip = getCameraField(markToolState.cameraNo, 'ip');

  if (!ip) {
    message.warn('请先设置相机IP');
    return;
  }

  markToolState.src = '';
  const b64Image = await V1ManageSysUnitTerminalCaptureIp({
    ip,
  });

  const imageB64Header = 'data:image/jpeg;base64,';
  markToolState.src = `${imageB64Header}${b64Image}`;
}

async function confirmEditor() {
  const cameraNo = markToolState.cameraNo;
  const otherNo = markToolState.cameraNo === 1 ? 2 : 1;

  const value = markToolState.points.length ? JSON.stringify(markToolState.points) : null;
  const otherValue = value ? null : getCameraField(otherNo, 'points');

  formActions.setFieldsValue({
    [getCameraFieldName(cameraNo, 'points')]: value,
    [getCameraFieldName(otherNo, 'points')]: otherValue,
  });

  await submitDetailModification();

  markToolState.visible = false;
}

function cancelEditor() {
  markToolState.visible = false;
}
</script>

<template>
  <div class="page-wrapper">
    <div class="tree-wrapper">
      <div class="tree-search-tool">
        <a-input v-model:value="treeState.search" placeholder="请输入关键字" allow-clear />
      </div>
      <div class="flex justify-end mb-1">
        <a-button type="link" @click="onAddBtnClick()"> 新增 </a-button>
      </div>
      <div class="tree">
        <a-tree
          v-if="filteredTreeData.length"
          :tree-data="filteredTreeData"
          :selectedKeys="treeState.selectedKeys"
          v-model:expandedKeys="treeState.expandedKeys"
          block-node
          @select=""
        >
          <template #title="node">
            <div class="tree-node-title" @click="onSelectTreeNode(node)">
              <span class="title truncate" :title="node.name">
                {{ node.name }}
              </span>
              <div class="tree-node-icons">
                <PlusOutlined
                  v-if="node.type === NodeTypeEnum.NODE"
                  @click.stop="onAddBtnClick(node)"
                />
                <DeleteOutlined @click.stop="onDeleteBtnClick(node)" />
              </div>
            </div>
          </template>
        </a-tree>
        <customizeRenderEmpty v-else />
      </div>
    </div>
    <div class="form-content">
      <div class="content-title">节点配置</div>
      <a-spin :spinning="updateStationDetail.loading">
        <BasicForm @register="registerForm"> </BasicForm>
      </a-spin>
      <a-spin :spinning="updateStationDetail.loading">
        <div class="mb-4" v-if="currentDetailInfo.data.value.type === NodeTypeEnum.STATION">
          <div class="content-title">相机标定 </div>
          <div class="flex gap-2">
            <div class="camera-setting-item">
              <div> 相机1 </div>
              <a-button type="primary" @click="showMarkEditor(1)">
                {{
                  currentDetailInfo.data.value.terminalDTO?.cameraOneCalibration?.length
                    ? '已配置'
                    : '未配置'
                }}
              </a-button>
            </div>
            <div class="camera-setting-item">
              <div> 相机2 </div>
              <a-button type="primary" @click="showMarkEditor(2)">
                {{
                  currentDetailInfo.data.value.terminalDTO?.cameraTwoCalibration?.length
                    ? '已配置'
                    : '未配置'
                }}
              </a-button>
            </div>
          </div>
        </div>
      </a-spin>
      <div class="project-info" v-if="currentDetailInfo.data.value.type === NodeTypeEnum.STATION">
        <StationInfo :station-id="currentDetailInfo.data.value.id!" />
      </div>
    </div>
    <a-modal
      title="相机标定"
      :maskClosable="false"
      :visible="markToolState.visible"
      width="900px"
      @ok="confirmEditor"
      @cancel="cancelEditor"
    >
      <div class="py-8">
        <PicMarkTool
          class="w-800px m-auto"
          v-model="markToolState.points"
          @reload-image="reloadMarkImage"
          :maxPointCount="4"
          :src="markToolState.src"
        />
      </div>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
.page-wrapper {
  display: flex;
  gap: 0px;
  height: 100%;
}

.tree-wrapper {
  padding: 10px 8px;
  width: 240px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #eee;
}

.tree {
  position: relative;
  flex: 1;
  height: 0;

  margin-right: -6px;
  padding-right: 6px;

  overflow: auto;
}

.tree-search-tool {
  margin-bottom: 8px;
}

.form-content {
  padding: 0px 12px;
  width: 0;
  flex: 1;
}

.tree-node-title {
  display: flex;
  width: 100%;
  gap: 4px;
  align-items: center;

  > .title {
    flex: 1;
    width: 0;
  }

  > .tree-node-icons {
    display: flex;
    gap: 4px;
    opacity: 0;
  }

  &:hover {
    .tree-node-icons {
      opacity: 1;
    }
  }
}

.project-info {
  margin-top: 8px;
}

.camera-setting-item {
  border-radius: 4px;

  border: 1px solid gray;
  padding: 8px 12px;

  display: inline-flex;
  align-items: center;
  gap: 16px;
}
</style>

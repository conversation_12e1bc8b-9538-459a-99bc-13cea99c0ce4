<template>
  <div class="card-login-container">
    <div class="card-status">
      <IdcardOutlined class="card-icon" />
      <p class="card-text">请刷身份证或工卡...</p>
    </div>
    <!-- 隐藏的输入框用于接收刷卡数据 -->
    <input 
      ref="cardInputRef" 
      class="card-input" 
      type="text" 
      @keyup.enter="handleCardInput"
      v-model="cardInput"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { IdcardOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useWebSocket } from '/@/utils/websocket';
import { useGlobSetting } from '/@/hooks/setting';
import md5 from 'crypto-js/md5';

interface CardLoginEmits {
  (e: 'loginSuccess', loginData: any): void;
}

interface Props {
  deviceIp?: string;
}

const props = withDefaults(defineProps<Props>(), {
  deviceIp: '',
});

const emit = defineEmits<CardLoginEmits>();

const { createMessage } = useMessage();
const { socketUrl } = useGlobSetting();

// 刷卡登录相关状态
const cardInputRef = ref();
const cardInput = ref('');
const wsInstance = ref<ReturnType<typeof useWebSocket> | null>(null);

// 初始化WebSocket连接
const initWebSocket = async () => {
  // 如果存在旧连接，先断开
  cleanupWebSocket();

  const ws = useWebSocket({
    url: `ws://${socketUrl}/ws`,
    headers: {
      deviceIp: props.deviceIp,
    },
    onMessage: handleWebSocketMessage,
    onError: (error) => console.error('刷卡登录WebSocket错误:', error),
    onClose: (event) => console.log('刷卡登录WebSocket连接已关闭:', event),
    onOpen: () => console.log('刷卡登录WebSocket连接已建立'),
    createMessage: {
      action: 'swipeCardLogin',
      content: {
        deviceIp: props.deviceIp,
      },
    },
    heartbeatMessage: {
      action: 'heartbeat',
      content: {},
    },
  });

  wsInstance.value = ws;

  // 立即连接
  ws.connect();
};

// 清理 WebSocket 连接
const cleanupWebSocket = () => {
  if (wsInstance.value) {
    wsInstance.value.disconnect();
    wsInstance.value = null;
  }
};

// 处理 WebSocket 消息
const handleWebSocketMessage = async (data: any) => {
  if (data.status == 'success') return;
  console.log('刷卡登录消息:', data);
  
  // 读卡成功，触发登录事件
  if (data.employeeId) {
    emit('loginSuccess', {
      username: md5(data.employeeId).toString(),
      password: data.employeeId,
      loginType: 4, // 刷卡登录类型
      employeeId: data.employeeId,
    });
  }
};

// 处理手动输入的卡号（备用方案）
const handleCardInput = () => {
  if (cardInput.value.trim()) {
    emit('loginSuccess', {
      username: md5(cardInput.value.trim()).toString(),
      password: cardInput.value.trim(),
      loginType: 4,
      employeeId: cardInput.value.trim(),
    });
    cardInput.value = '';
  }
};

// 聚焦输入框
const focusInput = () => {
  cardInputRef.value?.focus();
};

// 暴露方法给父组件
defineExpose({
  initWebSocket,
  cleanupWebSocket,
  focusInput,
});

// 组件挂载时初始化
onMounted(() => {
  focusInput();
  initWebSocket();
});

// 组件卸载时清理
onUnmounted(() => {
  cleanupWebSocket();
});
</script>

<style lang="less" scoped>
.card-login-container {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .card-status {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    background: rgba(0, 153, 107, 0.1);
    border: 1px solid rgba(0, 153, 107, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;

    .card-icon {
      font-size: 48px;
      color: #00996b;
      margin-bottom: 16px;
      animation: pulse 1.5s infinite;
      filter: drop-shadow(0 0 8px rgba(0, 153, 107, 0.3));
    }

    .card-text {
      margin: 0;
      color: rgba(255, 255, 255, 0.85);
      font-size: 16px;
    }
  }

  .card-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
    top: -9999px;
    left: -9999px;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
    filter: drop-shadow(0 0 12px rgba(0, 153, 107, 0.4));
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>

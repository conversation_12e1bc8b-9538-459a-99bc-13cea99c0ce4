<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="/resource/img/logo.png" alt="logo" class="logo" />
        <h1 class="title">道场智能培训系统</h1>
      </div>
      <div class="login-content">
        <a-tabs v-model:activeKey="activeKey" @change="handleTabChange">
          <a-tab-pane key="card" tab="刷卡登录">
            <div class="card-login-container">
              <div class="card-status">
                <IdcardOutlined class="card-icon" />
                <p class="card-text">请刷身份证或工卡...</p>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="face" tab="刷脸登录">
            <div class="face-login-container">
              <div class="face-status">
                <CameraOutlined class="face-icon" />
                <p class="face-text">请正对摄像头进行人脸识别...</p>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="password" tab="密码登录">
            <a-form :model="passwordForm" @finish="handlePasswordLogin">
              <a-form-item name="username" :rules="[{ required: true, message: '请输入账号' }]">
                <a-input
                  v-model:value="passwordForm.username"
                  size="large"
                  placeholder="请输入账号"
                >
                  <template #prefix>
                    <UserOutlined />
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item name="password" :rules="[{ required: true, message: '请输入密码' }]">
                <a-input-password
                  v-model:value="passwordForm.password"
                  size="large"
                  placeholder="请输入密码"
                >
                  <template #prefix>
                    <LockOutlined />
                  </template>
                </a-input-password>
              </a-form-item>
              <a-form-item>
                <a-button type="primary" html-type="submit" size="large" block :loading="loading">
                  登录
                </a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
          <a-tab-pane key="domain" tab="域账号登录">
            <a-form :model="domainForm" @finish="handleDomainLogin">
              <a-form-item name="username" :rules="[{ required: true, message: '请输入域账号' }]">
                <a-input
                  v-model:value="domainForm.username"
                  size="large"
                  placeholder="请输入域账号"
                >
                  <template #prefix>
                    <UserOutlined />
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item name="password" :rules="[{ required: true, message: '请输入密码' }]">
                <a-input-password
                  v-model:value="domainForm.password"
                  size="large"
                  placeholder="请输入密码"
                >
                  <template #prefix>
                    <LockOutlined />
                  </template>
                </a-input-password>
              </a-form-item>
              <a-form-item>
                <a-button type="primary" html-type="submit" size="large" block :loading="loading">
                  登录
                </a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import {
  UserOutlined,
  LockOutlined,
  IdcardOutlined,
  CameraOutlined,
} from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { userStore } from '/@/store/modules/user';
import { useGo } from '/@/hooks/web/usePage';
import type { LoginParams } from '/@/types/auth';
import {
  V1LocationHomeCurrentIp,
  V1OpenApiUserLoginGucPost,
  V1OpenApiUserLoginPublicKey,
} from '/@/api/cddc.req';
import { useWebSocket } from '/@/utils/websocket';
import { useGlobSetting } from '/@/hooks/setting';
// @ts-ignore
import JSEncrypt from 'jsencrypt';
import md5 from 'crypto-js/md5';

const { createMessage } = useMessage();
const go = useGo();
const { socketUrl } = useGlobSetting();

const activeKey = ref('card');
const loading = ref(false);
const cardInputRef = ref();
const wsInstance = ref<ReturnType<typeof useWebSocket> | null>(null);
const isCameraActive = ref(false);
const faceWsInstance = ref<ReturnType<typeof useWebSocket> | null>(null);
const ipAddress = ref<string>('');

// 密码登录表单
const passwordForm = ref<Pick<LoginParams, 'username' | 'password'>>({
  username: '',
  password: '',
});

// 域账号登录表单
const domainForm = ref<Pick<LoginParams, 'username' | 'password'>>({
  username: '',
  password: '',
});

// 处理标签页切换
const handleTabChange = (key: string) => {
  // 清理所有WebSocket连接
  cleanupWebSocket();
  cleanupFaceWebSocket();

  // 根据登录方式建立相应的连接
  switch (key) {
    case 'face':
      initFaceWebSocket();
      break;
    case 'card':
      initWebSocket();
      break;
    // 密码登录不需要建立WebSocket连接
    case 'password':
    default:
      break;
  }
};

// 获取本机IP地址
const fetchIpAddress = async () => {
  try {
    const response = await V1LocationHomeCurrentIp();
    if (response) {
      ipAddress.value = response;
      return ipAddress.value;
    } else {
      console.warn('获取IP地址返回了意外的格式:', response);
      return '';
    }
  } catch (error) {
    console.error('获取IP地址失败:', error);
    return '';
  }
};

// 初始化WebSocket连接
const initWebSocket = async () => {
  // 如果存在旧连接，先断开
  cleanupWebSocket();

  // 确保IP地址已获取
  if (!ipAddress.value) {
    await fetchIpAddress();
  }

  const ws = useWebSocket({
    url: `ws://${socketUrl}/ws`,
    headers: {
      deviceIp: ipAddress.value,
    },
    onMessage: handleWebSocketMessage,
    onError: (error) => console.error('WebSocket错误:', error),
    onClose: (event) => console.log('WebSocket连接已关闭:', event),
    onOpen: () => console.log('WebSocket连接已建立'),
    createMessage: {
      action: 'swipeCardLogin',
      content: {
        deviceIp: ipAddress.value, // 同时在消息内容中也传递IP
      },
    },
    heartbeatMessage: {
      action: 'heartbeat',
      content: {},
    },
  });

  wsInstance.value = ws;

  // 立即连接
  ws.connect();
};

// 清理 WebSocket 连接
const cleanupWebSocket = () => {
  if (wsInstance.value) {
    wsInstance.value.disconnect();
    wsInstance.value = null;
  }
};

// 处理 WebSocket 消息
const handleWebSocketMessage = async (data: any) => {
  if (data.status == 'success') return;
  console.log('----msg---data :', data);
  // 读卡成功，自动登录
  if (data.employeeId) {
    try {
      loading.value = true;
      await handlePasswordLogin({
        username: md5(data.employeeId).toString(),
        password: data.employeeId,
        loginType: 4,
      });
      loading.value = false;
    } catch (error) {
      createMessage.error('登录失败，请重试');
    } finally {
      loading.value = false;
    }
  }
};

// 初始化人脸识别WebSocket连接
const initFaceWebSocket = async () => {
  cleanupFaceWebSocket();

  // 确保IP地址已获取
  if (!ipAddress.value) {
    await fetchIpAddress();
  }

  const ws = useWebSocket({
    url: `ws://${socketUrl}/ws`,
    headers: {
      deviceIp: ipAddress.value,
    },
    onMessage: handleWebSocketMessage,
    onError: (error) => console.error('人脸识别WebSocket错误:', error),
    onClose: (event) => {
      console.log('人脸识别WebSocket连接已关闭:', event);
      isCameraActive.value = false;
    },
    onOpen: () => {
      console.log('人脸识别WebSocket连接已建立');
      isCameraActive.value = true;
    },
    createMessage: {
      action: 'swipeCardLogin',
      content: {
        deviceIp: ipAddress.value, // 同时在消息内容中也传递IP
      },
    },
    heartbeatMessage: {
      action: 'heartbeat',
      content: {},
    },
  });

  faceWsInstance.value = ws;
  ws.connect();
};

// 清理人脸识别WebSocket连接
const cleanupFaceWebSocket = () => {
  if (faceWsInstance.value) {
    faceWsInstance.value.disconnect();
    faceWsInstance.value = null;
  }
  isCameraActive.value = false;
};

// 页面加载时自动聚焦刷卡输入框并建立WebSocket连接
onMounted(async () => {
  cardInputRef.value?.focus();
  // 先获取IP地址
  await fetchIpAddress();

  // 由于默认是刷卡登录，所以需要建立刷卡的WebSocket连接
  initWebSocket();
});

// 组件卸载时清理WebSocket连接
onUnmounted(() => {
  cleanupWebSocket();
  cleanupFaceWebSocket();
});

// 密码登录
const handlePasswordLogin = async (values: LoginParams) => {
  try {
    loading.value = true;

    // 设置登录超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('登录超时'));
      }, 5000);
    });

    const loginPublicKey = await Promise.race([
      V1OpenApiUserLoginPublicKey({ username: values.username }),
      timeoutPromise,
    ]);

    if (typeof loginPublicKey !== 'string') {
      throw new Error('获取公钥失败');
    }

    // 使用 JSEncrypt 加密密码
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(loginPublicKey);
    const encryptedPassword = encrypt.encrypt(values.password);

    if (!encryptedPassword) {
      throw new Error('密码加密失败');
    }

    const loginInfo = await Promise.race([
      V1OpenApiUserLoginGucPost({
        ...values,
        password: encryptedPassword,
        loginType: values.loginType || 1,
      }),
      timeoutPromise,
    ]);

    await userStore.setLoginToken(loginInfo);
    await afterLogin();
  } catch (error) {
    if (error instanceof Error && error.message === '登录超时') {
      createMessage.error('登录超时，页面将自动刷新');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else {
      createMessage.error('登录失败，请检查账号密码');
    }
  } finally {
    loading.value = false;
  }
};

// 域账号登录
const handleDomainLogin = async (values: LoginParams) => {
  try {
    loading.value = true;

    // 设置登录超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('登录超时'));
      }, 5000);
    });

    const loginPublicKey = await Promise.race([
      V1OpenApiUserLoginPublicKey({ username: values.username }),
      timeoutPromise,
    ]);

    if (typeof loginPublicKey !== 'string') {
      throw new Error('获取公钥失败');
    }

    // 使用 JSEncrypt 加密密码
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(loginPublicKey);
    const encryptedPassword = encrypt.encrypt(values.password);

    if (!encryptedPassword) {
      throw new Error('密码加密失败');
    }

    const loginInfo = await Promise.race([
      V1OpenApiUserLoginGucPost({
        ...values,
        password: encryptedPassword,
        loginType: 3, // 域账号登录类型
      }),
      timeoutPromise,
    ]);

    await userStore.setLoginToken(loginInfo);
    await afterLogin();
  } catch (error) {
    if (error instanceof Error && error.message === '登录超时') {
      createMessage.error('登录超时，页面将自动刷新');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else {
      createMessage.error('登录失败，请检查账号密码');
    }
  } finally {
    loading.value = false;
  }
};

// 登录成功后的处理
const afterLogin = async () => {
  // createMessage.success('登录成功');
  await userStore.recordLoginTime();
  // 获取重定向地址
  go('/home');
};
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #1a1a1a;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 153, 107, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 75% 75%, rgba(0, 153, 107, 0.1) 0%, transparent 40%);

  .login-box {
    width: 440px;
    padding: 40px;
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid rgba(0, 153, 107, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 12px 48px rgba(0, 153, 107, 0.2);
      border-color: rgba(0, 153, 107, 0.3);
    }

    .login-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 40px;

      .logo {
        width: 48px;
        height: 48px;
        margin-right: 16px;
        transition: transform 0.3s ease;
        filter: brightness(0.9);
      }

      .title {
        margin: 0;
        font-size: 30px;
        font-weight: 600;
        color: #00996b;
        text-shadow: 0 0 20px rgba(0, 153, 107, 0.3);
      }
    }

    .login-content {
      :deep(.cddc-ant-tabs-nav) {
        margin-bottom: 32px;
      }

      :deep(.cddc-ant-tabs-content) {
        min-height: 220px;
      }

      :deep(.cddc-ant-tabs-tabpane) {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      :deep(.cddc-ant-tabs-tab) {
        transition: all 0.3s ease;
        color: rgba(255, 255, 255, 0.65);
        padding: 12px 16px; // 增加标签页内边距
        min-width: 80px; // 设置最小宽度
        text-align: center;

        &:hover {
          color: #00996b;
        }

        &.cddc-ant-tabs-tab-active .cddc-ant-tabs-tab-btn {
          color: #00996b;
        }
      }

      :deep(.cddc-ant-tabs-ink-bar) {
        background: #00996b;
      }

      :deep(.cddc-ant-input-affix-wrapper) {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(0, 153, 107, 0.2);
        border-radius: 8px;
        transition: all 0.3s ease;

        input {
          background-color: transparent;
          color: rgba(255, 255, 255, 0.85);

          &::placeholder {
            color: rgba(255, 255, 255, 0.25);
          }
        }

        .anticon {
          color: rgba(255, 255, 255, 0.45);
        }

        &:hover,
        &:focus {
          border-color: #00996b;
          box-shadow: 0 0 0 2px rgba(0, 153, 107, 0.1);
          background-color: rgba(255, 255, 255, 0.08);
        }
      }

      :deep(.cddc-ant-btn-primary) {
        height: 44px;
        font-size: 16px;
        border-radius: 8px;
        background: #00996b;
        border: none;
        transition: all 0.3s ease;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        box-shadow: 0 4px 12px rgba(0, 153, 107, 0.3);

        &:hover {
          opacity: 0.9;
          box-shadow: 0 6px 16px rgba(0, 153, 107, 0.4);
        }
      }
    }

    .face-login-container,
    .card-login-container {
      height: 220px;
      display: flex;
      align-items: center;
      justify-content: center;

      .face-status,
      .card-status {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32px;
        background: rgba(0, 153, 107, 0.1);
        border: 1px solid rgba(0, 153, 107, 0.2);
        border-radius: 12px;
        transition: all 0.3s ease;

        .face-icon,
        .card-icon {
          font-size: 48px;
          color: #00996b;
          margin-bottom: 16px;
          animation: pulse 1.5s infinite;
          filter: drop-shadow(0 0 8px rgba(0, 153, 107, 0.3));
        }

        .face-text,
        .card-text {
          margin: 0;
          color: rgba(255, 255, 255, 0.85);
          font-size: 16px;
        }
      }
    }

    .card-login-container {
      .card-input {
        position: absolute;
        opacity: 0;
        pointer-events: none;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
    filter: drop-shadow(0 0 12px rgba(0, 153, 107, 0.4));
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
